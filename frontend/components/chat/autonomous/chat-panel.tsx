'use client';

import { memo, useCallback, useEffect, useMemo, useReducer } from 'react';

import Image from 'next/image';

import { useChatContext } from '@/app/(home)/agents/_components/chat-context';
import {
  Dashboard,
  MessageDisplayComponentPublic,
  Report,
} from '@/client/types.gen';
import { If } from '@/components/ui/common/if';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

import { MessageInput } from '../message-input';
import { Session, ToolCall } from '../types';
import { ChartsCanvas } from './canvas';
import {
  Chat<PERSON><PERSON>lHeaderA<PERSON><PERSON>ontent,
  ChatPanelHeaderContent,
} from './chat-panel-header';
import { AutonomousMessageList } from './message-list';

interface ChatContainerProps {
  session?: Session;
  onSendMessage: (
    content: string,
    attachmentIds?: string[],
    resourceId?: string,
  ) => void;
  onStopStreaming?: () => void;
  onNewChat?: () => void;
  inputSectionChildren?: React.ReactNode;
  confirmation?: InterruptConfirmation | null;
  isCreatingConversation?: boolean;
  quotaInfo?: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
  resourceId?: string;
  isSharedView?: boolean;
  currentReport?: Report | null;
  currentDashboard?: Dashboard | null;
  conversationId?: string;
}

// Default panel sizes
const DEFAULT_CHAT_PANEL_SIZE = 65;
const DEFAULT_CANVAS_PANEL_SIZE = 35;

// Local storage key for panel sizes
const PANEL_SIZES_STORAGE_KEY = 'autonomous-chat-panel-sizes';

// Define action types for our reducer
type ChatPanelAction =
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_UNREAD_COUNT'; payload: number }
  | { type: 'INCREMENT_UNREAD_COUNT' }
  | { type: 'RESET_UNREAD_COUNT' }
  | { type: 'TOGGLE_CANVAS'; payload: boolean }
  | { type: 'SET_MANUALLY_HIDDEN'; payload: boolean }
  | { type: 'SET_INPUT_VALUE'; payload: string }
  | { type: 'SET_HIDDEN_ROLES'; payload: string[] }
  | { type: 'TOGGLE_ROLE_VISIBILITY'; payload: string }
  | { type: 'UPDATE_TOOL_CALLS'; payload: ToolCall[] }
  | { type: 'UPDATE_PANEL_SIZES'; payload: number[] }
  | { type: 'RESET_STATE'; payload?: { sessionId?: string } }
  | { type: 'UPDATE_RESOURCE_ID'; payload: string }
  | { type: 'UPDATE_CANVAS_ITEMS'; payload: CanvasItemWithAgent[] }
  | { type: 'UPDATE_PLANNING_TOOLS'; payload: ToolCall[] };

// Define state interface
interface ChatPanelState {
  activeTab: string;
  unreadCount: number;
  isCanvasVisible: boolean;
  manuallyHidden: boolean;
  inputValue: string;
  hiddenRoles: string[];
  activeToolCalls: ToolCall[];
  displayComponents: MessageDisplayComponentPublic[];
  canvasItemsWithAgent: CanvasItemWithAgent[];
  panelSizes: number[];
  resourceId?: string;
  planningTools: ToolCall[];
}

// Initial state
const initialState: ChatPanelState = {
  activeTab: 'chat',
  unreadCount: 0,
  isCanvasVisible: true,
  manuallyHidden: false,
  inputValue: '',
  hiddenRoles: ['assistant', 'system'],
  activeToolCalls: [],
  displayComponents: [],
  canvasItemsWithAgent: [],
  panelSizes: [DEFAULT_CHAT_PANEL_SIZE, DEFAULT_CANVAS_PANEL_SIZE],
  planningTools: [],
};

// Reducer function
function chatPanelReducer(
  state: ChatPanelState,
  action: ChatPanelAction,
): ChatPanelState {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      // Set active tab and reset unread count when switching to the chat tab
      return {
        ...state,
        activeTab: action.payload,
        unreadCount: action.payload === 'chat' ? 0 : state.unreadCount,
      };

    case 'SET_UNREAD_COUNT':
      return { ...state, unreadCount: action.payload };

    case 'INCREMENT_UNREAD_COUNT':
      return { ...state, unreadCount: state.unreadCount + 1 };

    case 'RESET_UNREAD_COUNT':
      return { ...state, unreadCount: 0 };

    case 'TOGGLE_CANVAS':
      return {
        ...state,
        isCanvasVisible: action.payload,
        // Update manually hidden state when toggling canvas
        manuallyHidden: action.payload ? false : true,
      };

    case 'SET_MANUALLY_HIDDEN':
      return { ...state, manuallyHidden: action.payload };

    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload };

    case 'SET_HIDDEN_ROLES':
      return { ...state, hiddenRoles: action.payload };

    case 'TOGGLE_ROLE_VISIBILITY':
      return {
        ...state,
        hiddenRoles: state.hiddenRoles.includes(action.payload)
          ? state.hiddenRoles.filter((r) => r !== action.payload)
          : [...state.hiddenRoles, action.payload],
      };

    case 'UPDATE_TOOL_CALLS':
      return {
        ...state,
        activeToolCalls: action.payload,
        // Auto-show canvas logic is in the useEffect, not here
      };

    case 'UPDATE_PANEL_SIZES':
      // Save panel sizes to localStorage for persistence
      try {
        localStorage.setItem(
          PANEL_SIZES_STORAGE_KEY,
          JSON.stringify(action.payload),
        );
      } catch (error) {
        console.error('Failed to save panel sizes to localStorage:', error);
      }
      return {
        ...state,
        panelSizes: action.payload,
      };

    case 'RESET_STATE':
      return {
        ...initialState,
        activeTab: 'chat',
        manuallyHidden: false,
        isCanvasVisible: true,
        hiddenRoles: state.hiddenRoles,
        panelSizes: state.panelSizes,
        planningTools: [], // Explicitly reset planning tools
      };

    case 'UPDATE_RESOURCE_ID':
      return { ...state, resourceId: action.payload };

    case 'UPDATE_CANVAS_ITEMS':
      return {
        ...state,
        canvasItemsWithAgent: action.payload,
        displayComponents: action.payload
          .filter((item) => item.type === 'displayComponent')
          .map((item) => item.content as MessageDisplayComponentPublic),
        // Remove auto-show canvas logic for now
        isCanvasVisible: state.isCanvasVisible,
      };

    case 'UPDATE_PLANNING_TOOLS':
      return {
        ...state,
        planningTools: action.payload,
      };

    default:
      return state;
  }
}

// Memoized components
const MemoizedAutonomousMessageList = memo(AutonomousMessageList);

// Update the interface to include message index for ordering
interface CanvasItemWithAgent {
  id: string;
  type: 'toolCall' | 'displayComponent';
  content: ToolCall | MessageDisplayComponentPublic;
  agentName?: string;
  agentRole?: string;
  messageIndex: number; // Track original message position
}

function TabAutonomousChatContainerInner({
  session,
  onSendMessage,
  onStopStreaming,
  onNewChat,
  inputSectionChildren,
  confirmation,
  isCreatingConversation = false,
  quotaInfo,
  resourceId,
  isSharedView = false,
  currentReport,
  currentDashboard,
  conversationId,
}: ChatContainerProps) {
  const { messages, isStreaming } = useChatContext();

  // Initialize state with saved panel sizes if available
  const initialStateWithSavedSizes = useMemo(() => {
    let sizes = [DEFAULT_CHAT_PANEL_SIZE, DEFAULT_CANVAS_PANEL_SIZE];
    if (typeof window !== 'undefined') {
      try {
        const savedSizes = localStorage.getItem(PANEL_SIZES_STORAGE_KEY);
        if (savedSizes) {
          const parsedSizes = JSON.parse(savedSizes);
          if (Array.isArray(parsedSizes) && parsedSizes.length === 2) {
            sizes = parsedSizes;
          }
        }
      } catch (error) {
        console.error('Failed to load panel sizes from localStorage:', error);
      }
    }

    // Always set canvas to visible by default
    return {
      ...initialState,
      isCanvasVisible: true,
      manuallyHidden: false,
      panelSizes: sizes,
    };
  }, []);

  // Use the memoized initial state
  const [state, dispatch] = useReducer(
    chatPanelReducer,
    initialStateWithSavedSizes,
  );

  const isMobile = useIsMobile();

  // Get window size for responsive behavior
  // const { width } = useWindowSize();

  // Responsive breakpoint for hiding canvas

  // Update the width effect to handle canvas visibility
  // useEffect(() => {
  //   const isVerySmallScreen = width < 1100;
  //   dispatch({ type: 'TOGGLE_CANVAS', payload: !isVerySmallScreen });
  //   dispatch({ type: 'SET_MANUALLY_HIDDEN', payload: false });
  // }, [width]);

  // Reset state when session changes
  useEffect(() => {
    if (session?.id) {
      // Only reset non-essential parts of state to preserve context
      dispatch({
        type: 'RESET_STATE',
        payload: {
          sessionId: session.id,
        },
      });
    }
  }, [session?.id]);

  // Update context when resourceId changes
  useEffect(() => {
    if (resourceId) {
      dispatch({
        type: 'UPDATE_RESOURCE_ID',
        payload: resourceId,
      });
    }
  }, [resourceId]);

  // Combined effect to extract both display components and tool calls from messages
  useEffect(() => {
    const extractedDisplayComponents: CanvasItemWithAgent[] = [];
    const allToolCalls: ToolCall[] = [];
    const toolsWithAgentInfo: CanvasItemWithAgent[] = [];

    // Process messages to extract both display components and tool calls
    messages.forEach((message, messageIndex) => {
      // Extract agent information from the message
      const agentName = message.role !== 'user' ? message.role : undefined;
      const agentRole = message.role;

      // Extract display components with agent information
      if (message.displayComponents && message.displayComponents.length > 0) {
        message.displayComponents.forEach((component) => {
          extractedDisplayComponents.push({
            id: component.id,
            type: 'displayComponent',
            content: component,
            agentName,
            agentRole,
            messageIndex,
          });
        });
      }

      // Extract tool calls with agent information
      if (message.toolCalls && message.toolCalls.length > 0) {
        message.toolCalls.forEach((toolCall) => {
          // Create a new tool call with agent info
          const toolCallWithAgent = {
            ...toolCall,
            agentName: agentName || 'assistant',
            agentRole: agentRole || 'assistant',
          };

          // Add to the plain tool calls array with agent info attached
          allToolCalls.push(toolCallWithAgent);

          // Add to the tool calls with agent info
          toolsWithAgentInfo.push({
            id: toolCall.id,
            type: 'toolCall',
            content: toolCallWithAgent, // Use the enhanced tool call with agent info
            agentName,
            agentRole,
            messageIndex,
          });
        });
      }
    });

    // Combine both display components and tool calls
    const allCanvasItems = [
      ...extractedDisplayComponents,
      ...toolsWithAgentInfo,
    ];

    // Update state with extracted components - use a single combined action
    dispatch({ type: 'UPDATE_CANVAS_ITEMS', payload: allCanvasItems });

    // Update state with all extracted tool calls
    if (allToolCalls.length > 0) {
      dispatch({ type: 'UPDATE_TOOL_CALLS', payload: allToolCalls });
    }
  }, [messages, session?.id, isStreaming]);

  const handleNewMessage = useCallback(
    (content: string, attachmentIds?: string[], resourceId?: string) => {
      onSendMessage(content, attachmentIds, resourceId);

      // Clear the input value when sending a message
      dispatch({ type: 'SET_INPUT_VALUE', payload: '' });

      // Increment badge counter if not on group-chat tab
      if (state.activeTab !== 'group-chat') {
        dispatch({ type: 'INCREMENT_UNREAD_COUNT' });
      }
    },
    [state.activeTab, onSendMessage],
  );

  const handleInputChange = useCallback((value: string) => {
    dispatch({ type: 'SET_INPUT_VALUE', payload: value });
  }, []);

  // Handle panel resize
  const handlePanelResize = useCallback((sizes: number[]) => {
    dispatch({ type: 'UPDATE_PANEL_SIZES', payload: sizes });
  }, []);

  // Handle planning tools change
  const handlePlanningToolsChange = useCallback((tools: ToolCall[]) => {
    dispatch({ type: 'UPDATE_PLANNING_TOOLS', payload: tools });
  }, []);

  const handleNewChatCb = useCallback(() => {
    // Reset canvas state
    dispatch({ type: 'RESET_STATE' });
    // Call the original onNewChat handler
    onNewChat?.();
  }, [onNewChat]);

  // Simplified check for input disabled state
  const isInputDisabled = isStreaming;

  // If shared view, disable canvas and message input
  if (isSharedView) {
    return (
      <div className="bg-background dark:bg-background flex h-full w-full flex-col items-center">
        <div className="flex w-full flex-col items-center pt-8 pb-4">
          <div className="border-border bg-background/80 dark:bg-popover/80 flex flex-col items-center rounded-lg px-6 py-4 backdrop-blur-md">
            <a
              href={process.env.NEXT_PUBLIC_APP_URL || '/'}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                src="/logo.svg"
                alt="CloudThinker Logo"
                width={96 * 3}
                height={128 * 3}
                priority
                aria-label="CloudThinker Logo"
              />
            </a>
            <span className="text-muted-foreground text-xs font-semibold tracking-wide sm:text-sm">
              Powered by{' '}
              <span className="text-primary font-bold">CloudThinker</span>
            </span>
          </div>
        </div>
        <div className="custom-scrollbar mx-auto w-full max-w-5xl flex-1 overflow-y-auto px-2 sm:px-0">
          <MemoizedAutonomousMessageList
            messages={messages}
            isLoading={isStreaming}
            confirmation={null}
            isSharedView={isSharedView}
          />
        </div>
      </div>
    );
  }

  const renderChatArea = () => {
    return (
      <div className="@container flex h-full flex-col">
        <div className="flex justify-between py-1">
          <ChatPanelHeaderContent
            isCreatingConversation={isCreatingConversation}
            conversationId={session?.id}
            conversationTitle={session?.title}
            conversationCreatedAt={session?.timestamp?.toISOString()}
            modelProvider="bedrock"
            resourceId={state.resourceId || resourceId}
          />

          <ChatPanelHeaderActionsContent
            onNewChat={handleNewChatCb}
            isCreatingConversation={isCreatingConversation}
            conversationId={session?.id}
            isSharedView={isSharedView}
          />
        </div>

        <div className="flex grow flex-col overflow-auto">
          {/* Chat tab content */}
          <div
            className={cn('flex h-full w-full flex-col pr-0 sm:pr-2', {
              hidden: state.activeTab !== 'chat',
            })}
          >
            <div className="custom-scrollbar grow overflow-y-auto">
              <MemoizedAutonomousMessageList
                messages={messages}
                isLoading={isStreaming}
                confirmation={state.activeTab === 'chat' ? confirmation : null}
                isSharedView={isSharedView}
              />
            </div>
            {!isSharedView && (
              <div
                className={cn('flex w-full flex-col justify-end', {
                  'grow overflow-y-auto': messages.length === 0,
                })}
              >
                {/* <PlanningSession/> */}
                <MessageInput
                  value={state.inputValue}
                  onChange={handleInputChange}
                  onSendMessage={handleNewMessage}
                  disabled={isInputDisabled}
                  isStreaming={isStreaming}
                  onStop={onStopStreaming}
                  showExampleQuestions={true}
                  isEmptyConversation={messages.length === 0}
                  quotaInfo={quotaInfo}
                />
                {inputSectionChildren}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderChartCanvas = () => {
    return (
      <ChartsCanvas
        toolCalls={state.activeToolCalls}
        displayComponents={state.displayComponents}
        canvasItemsWithAgent={state.canvasItemsWithAgent}
        conversationId={conversationId}
        currentReport={currentReport}
        currentDashboard={currentDashboard}
        onPlanningToolsChange={handlePlanningToolsChange}
        isEmptyConversation={messages.length === 0}
      />
    );
  };

  return (
    <div
      className="flex h-full w-full flex-col"
      style={{
        contain: 'layout size style',
        boxSizing: 'border-box',
        position: 'relative',
        maxHeight: '100dvh',
        transform: 'translateZ(0)',
        backfaceVisibility: 'hidden',
        overscrollBehavior: 'none',
      }}
    >
      {/* Main container with proper layout for tool canvas */}
      <div
        className="relative flex h-full w-full flex-col"
        style={{
          contain: 'layout size style',
          boxSizing: 'border-box',
          position: 'relative',
        }}
      >
        {/* Main content */}
        <div
          className="flex h-full w-full flex-1 flex-col overflow-hidden"
          style={{
            position: 'relative',
            contain: 'layout size style',
          }}
        >
          <If
            condition={!isMobile}
            fallback={
              <>
                <Tabs
                  defaultValue="chat"
                  className="flex size-full flex-col gap-4 overflow-hidden"
                >
                  <ScrollArea className="flex grow flex-col">
                    <TabsContent
                      value="chat"
                      className="mt-0 h-full grow overflow-hidden"
                    >
                      {renderChatArea()}
                    </TabsContent>
                    <TabsContent
                      value="console"
                      className="mt-0 h-full grow overflow-hidden"
                    >
                      {renderChartCanvas()}
                    </TabsContent>
                  </ScrollArea>
                  <TabsList className="w-full [&>*]:grow">
                    <TabsTrigger value="chat">Chat</TabsTrigger>
                    <TabsTrigger value="console">Console</TabsTrigger>
                  </TabsList>
                </Tabs>
              </>
            }
          >
            <ResizablePanelGroup
              direction="horizontal"
              onLayout={handlePanelResize}
              className="relative h-full"
            >
              {/* Main chat area panel */}
              <ResizablePanel defaultSize={state.panelSizes[0]} minSize={30}>
                {renderChatArea()}
              </ResizablePanel>

              {/* Resize handle */}
              <ResizableHandle withHandle />

              {/* Charts & Data Canvas panel */}
              <ResizablePanel defaultSize={state.panelSizes[1]} minSize={20}>
                {renderChartCanvas()}
              </ResizablePanel>
            </ResizablePanelGroup>
          </If>
        </div>
      </div>
    </div>
  );
}

export function TabAutonomousChatContainer() {
  const context = useChatContext();

  return (
    <TabAutonomousChatContainerInner
      session={context.currentSession}
      onSendMessage={context.onSendMessage}
      onStopStreaming={context.onStopStreaming}
      onNewChat={context.onNewChat}
      confirmation={context.confirmation}
      isCreatingConversation={context.isCreatingConversation}
      quotaInfo={context.quotaInfo}
      resourceId={context.resourceId}
      isSharedView={context.isSharedView}
      currentReport={context.currentReport}
      currentDashboard={context.currentDashboard}
      conversationId={context.conversationId}
    />
  );
}

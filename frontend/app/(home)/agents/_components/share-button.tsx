import { useState } from 'react';

import { createPortal } from 'react-dom';

import { ShareChatService } from '@/client';
import { Button } from '@/components/ui/button';
import pathsConfig from '@/config/paths.config';
import { useToast } from '@/hooks/use-toast';
import { Link, Loader2, Share2, X } from 'lucide-react';

interface ShareButtonProps {
  conversationId: string;
}

// Helper to fetch static share page HTML
async function fetchCurrentPageHtml() {
  const url = window.location.href;
  const response = await fetch(url);
  if (!response.ok) throw new Error('Failed to fetch current page');
  const html = await response.text();
  return html;
}

export function ShareButton({ conversationId }: ShareButtonProps) {
  const { toast } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [shareId, setShareId] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch share link status when modal opens
  const handleOpenModal = async () => {
    setIsModalOpen(true);
    setIsLoading(true);
    try {
      const response = await ShareChatService.getShareLink({ conversationId });
      setShareId(response.share_id);
    } catch {
      setShareId(undefined);
    } finally {
      setIsLoading(false);
    }
  };

  // Create or update share link
  const handleCreateOrUpdate = async () => {
    setIsLoading(true);
    try {
      const response = await ShareChatService.createShareLink({
        conversationId,
      });
      setShareId(response.share_id);
      toast({
        title: 'Share link created',
        description: 'The conversation is now shared',
      });
      // Fetch and print the current page HTML
      try {
        const html = await fetchCurrentPageHtml();
        console.log('Current page HTML:', html);
      } catch (err) {
        console.error('Failed to fetch current page:', err);
      }
    } catch {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to create or update share link',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Revoke share link
  const handleRevoke = async () => {
    setIsLoading(true);
    try {
      await ShareChatService.revokeShareLink({ conversationId });
      setShareId(undefined);
      toast({
        title: 'Share link revoked',
        description: 'The conversation is no longer shared',
      });
    } catch {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to revoke share link',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyLink = async () => {
    if (!shareId) return;
    await navigator.clipboard.writeText(
      new URL(
        pathsConfig.share.shareDetail(shareId),
        window.location.origin,
      ).toString(),
    );
    toast({
      title: 'Link copied',
      description: 'Share link copied to clipboard',
    });
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="gap-2"
        disabled={isLoading}
        onClick={handleOpenModal}
        aria-label="Share"
      >
        <Share2 className="size-4" />
        <span className="@max-md:hidden">Share</span>
      </Button>
      {isModalOpen && (
        <ShareModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          shareId={shareId}
          isLoading={isLoading}
          onCopy={handleCopyLink}
          onCreateOrUpdate={handleCreateOrUpdate}
          onRevoke={handleRevoke}
        />
      )}
    </>
  );
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  shareId?: string;
  isLoading: boolean;
  onCopy: () => void;
  onCreateOrUpdate: () => void;
  onRevoke: () => void;
}

export function ShareModal({
  isOpen,
  onClose,
  shareId,
  isLoading,
  onCopy,
  onCreateOrUpdate,
  onRevoke,
}: ShareModalProps) {
  if (!isOpen) return null;
  if (typeof window === 'undefined') return null;

  const modalContent = (
    <div className="bg-opacity-30 fixed inset-0 z-50 flex items-center justify-center bg-black">
      <div className="border-border bg-background text-foreground dark:bg-popover dark:text-popover-foreground relative w-full max-w-md rounded-lg border p-6 shadow-lg">
        <button
          className="text-muted-foreground hover:text-foreground focus:ring-primary absolute top-3 right-3 focus:ring-2 focus:outline-hidden"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>
        <h2 className="mb-2 text-lg font-semibold">
          Update public share link for this chat
        </h2>
        <p className="text-muted-foreground mb-4 text-sm">
          Name, custom instructions, and any messages you add after sharing will
          remain private.
        </p>
        {shareId ? (
          <>
            <div className="mb-4 flex items-center gap-2">
              <div className="relative flex-1">
                <input
                  type="text"
                  className="border-border bg-input text-foreground focus:ring-primary dark:bg-input dark:text-foreground h-10 w-full rounded-lg border px-3 py-2 pr-10 focus:ring-2"
                  value={new URL(
                    pathsConfig.share.shareDetail(shareId),
                    window.location.origin,
                  ).toString()}
                  readOnly
                  aria-label="Shareable link"
                />
                <button
                  type="button"
                  onClick={onCopy}
                  disabled={isLoading}
                  className="hover:bg-muted absolute top-1/2 right-2 -translate-y-1/2 rounded-lg p-1 focus:outline-hidden"
                  aria-label="Copy share link"
                  title="Copy share link"
                >
                  <Link className="h-4 w-4" />
                </button>
              </div>
              <Button
                variant="secondary"
                className="h-8 px-3 text-sm font-medium"
                onClick={onCreateOrUpdate}
                disabled={isLoading}
                aria-label="Update share link"
                title="Update share link"
              >
                {isLoading ? (
                  <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                ) : null}{' '}
                Update link
              </Button>
            </div>
            <Button
              variant="destructive"
              className="mb-2 h-10 w-full text-base font-semibold"
              onClick={onRevoke}
              disabled={isLoading}
              aria-label="Revoke share link"
            >
              {isLoading ? (
                <span className="mr-2 animate-spin">
                  <Loader2 className="h-4 w-4" />
                </span>
              ) : (
                <X className="mr-1 h-4 w-4" />
              )}{' '}
              Revoke link
            </Button>
          </>
        ) : (
          <Button
            variant="default"
            className="mb-2 h-10 w-full text-base font-semibold"
            onClick={onCreateOrUpdate}
            disabled={isLoading}
            aria-label="Create share link"
          >
            {isLoading ? (
              <span className="mr-2 animate-spin">
                <Loader2 className="h-4 w-4" />
              </span>
            ) : (
              <Share2 className="mr-1 h-4 w-4" />
            )}{' '}
            Create share link
          </Button>
        )}
      </div>
    </div>
  );
  return createPortal(modalContent, document.body);
}

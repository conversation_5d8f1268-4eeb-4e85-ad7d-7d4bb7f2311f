import { useCallback, useEffect, useReducer } from 'react';

import { useMessageStream } from '@/hooks/use-autonomous-message-stream';

interface ChatState {
  selectedConversation: string | null;
  mounted: boolean;
  initialMessageSent: boolean;
}

type ChatAction =
  | { type: 'SET_MOUNTED'; payload: boolean }
  | { type: 'SET_SELECTED_CONVERSATION'; payload: string | null }
  | { type: 'SET_INITIAL_MESSAGE_SENT'; payload: boolean };

const initialState: ChatState = {
  selectedConversation: null,
  mounted: false,
  initialMessageSent: false,
};

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_MOUNTED':
      return { ...state, mounted: action.payload };
    case 'SET_SELECTED_CONVERSATION':
      return { ...state, selectedConversation: action.payload };
    case 'SET_INITIAL_MESSAGE_SENT':
      return { ...state, initialMessageSent: action.payload };
    default:
      return state;
  }
};

interface AutonomousChatProps {
  conversationId?: string;
}

export const useAutonomousChat = ({ conversationId }: AutonomousChatProps) => {
  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    selectedConversation: conversationId || null,
  });

  const messageStream = useMessageStream(state.selectedConversation);

  const {
    isStreaming,
    streamingMessages,
    handleSendMessage: originalHandleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    thinkingContent,
    planningContent,
    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,
  } = messageStream;

  const handleSendMessage = useCallback(
    (content: string, attachmentIds?: string[], resourceId?: string) => {
      originalHandleSendMessage(content, attachmentIds, resourceId);
    },
    [originalHandleSendMessage],
  );

  const setMounted = useCallback((mounted: boolean) => {
    dispatch({ type: 'SET_MOUNTED', payload: mounted });
  }, []);

  const setSelectedConversation = useCallback(
    (conversationId: string | null) => {
      dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: conversationId });
    },
    [],
  );

  // Clear streaming messages when selected conversation changes
  useEffect(() => {
    clearStreamingMessages();
  }, [state.selectedConversation, clearStreamingMessages]);

  const setInitialMessageSent = useCallback((sent: boolean) => {
    dispatch({ type: 'SET_INITIAL_MESSAGE_SENT', payload: sent });
  }, []);

  return {
    // State
    selectedConversation: state.selectedConversation,
    mounted: state.mounted,
    initialMessageSent: state.initialMessageSent,

    // Actions
    setMounted,
    setSelectedConversation,
    setInitialMessageSent,

    // Message stream
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    thinkingContent,
    planningContent,

    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,
  };
};

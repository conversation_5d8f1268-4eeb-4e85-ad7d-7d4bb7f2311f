import { ReactNode, createContext, useContext } from 'react';

import { Dashboard, RecommendationPublic, Report } from '@/client/types.gen';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { Message, Session } from '@/types/chat';

export type ChatType = 'resource' | 'agent';

interface QuotaInfo {
  quota_used: number;
  quota_limit: number;
  quota_remaining: number;
  usage_percentage: number;
}
interface PlanningContent {
  agent_name: string;
  planning: Array<{
    id: number;
    content: string;
    status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
    notes: string | null;
  }>;
}

interface ChatContextType {
  chatType: ChatType;
  messages: Message[];
  onSendMessage: (
    message: string,
    attachmentIds?: string[],
    resourceId?: string,
  ) => void;
  isStreaming: boolean;
  currentSession?: Session;
  onNewChat?: () => void;
  onStopStreaming?: () => void;
  confirmation?: InterruptConfirmation | null;
  isCreatingConversation?: boolean;
  streamingRecommendations?: RecommendationPublic[];
  setStreamingRecommendations?: React.Dispatch<
    React.SetStateAction<RecommendationPublic[]>
  >;
  quotaInfo?: QuotaInfo;
  resourceId?: string;
  isSharedView?: boolean;
  currentReport?: Report | null;
  currentDashboard?: Dashboard | null;
  conversationId?: string;
  thinkingContent?: string | null;
  planningContent?: PlanningContent;
  conversationResourceId?: string | null;
  hasReport?: boolean;
  hasDashboard?: boolean;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
  value: ChatContextType;
}

export function ChatProvider({ children, value }: ChatProviderProps) {
  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}

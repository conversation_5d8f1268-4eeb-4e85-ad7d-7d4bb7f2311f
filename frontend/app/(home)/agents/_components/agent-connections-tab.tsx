'use client';

import { useMemo } from 'react';
import { SchemaAgentPublic, ConnectionType } from '@/openapi-ts/gens';
import { useAgentContext } from '@/features/agent/provider/agent-provider';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { useConnectionList } from '@/features/connection/hooks/use-connection-list';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { If } from '@/components/ui/common/if';
import { Settings, Plus, Minus, Link } from 'lucide-react';

interface AgentConnectionsTabProps {
  agent: SchemaAgentPublic;
}

export function AgentConnectionsTab({ agent }: AgentConnectionsTabProps) {
  const { agentsConnections } = useAgentContext();

  // Get current agent connections (exclude CLOUD type)
  const currentConnections = useMemo(() => {
    const agentConnections = agentsConnections?.agents_connections?.find(
      (agentConn) => agentConn.agent_id === agent.id
    )?.connections || [];
    return agentConnections;
  }, [agentsConnections, agent.id]);

  // Get available connections (exclude CLOUD type)
  const { installedConnections: builtinConnections, isLoading: isLoadingBuiltin } = useConnectionList({ type: ConnectionType.builtin });
  const { installedMcpConnections: mcpConnections, isLoading: isLoadingMcp } = useConnectionList({ type: ConnectionType.mcp });

  // Mutation hooks
  const { mutate: createConnection, isPending: isCreating } = agentQuery.mutation.useCreateConnection(agent.id);
  const { mutate: deleteConnection, isPending: isDeleting } = agentQuery.mutation.useDeleteConnection(agent.id);

  // Available connections (not currently assigned to agent)
  const availableBuiltinConnections = builtinConnections.filter(conn =>
    !currentConnections.some(current => current.id === conn.id)
  );
  const availableMcpConnections = mcpConnections.filter(conn =>
    !currentConnections.some(current => current.id === conn.id)
  );

  const handleAddConnection = (connectionId: string) => {
    createConnection(connectionId);
  };

  const handleRemoveConnection = (connectionId: string) => {
    deleteConnection(connectionId);
  };

  const isLoading = isLoadingBuiltin || isLoadingMcp || isCreating || isDeleting;

  return (
    <div className="flex flex-1 flex-col overflow-hidden">
      <div className="flex-1 overflow-y-auto space-y-6 p-1">
        {/* Current Connections */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-foreground">Current Connections</h4>
            <Badge variant="secondary" size="sm">{currentConnections.length}</Badge>
          </div>

          <If
            condition={currentConnections.length > 0}
            fallback={
              <div className="rounded-lg border border-dashed bg-muted/20 p-6 text-center">
                <Settings className="mx-auto h-8 w-8 text-muted-foreground/50" />
                <p className="mt-2 text-sm text-muted-foreground">
                  No connections assigned
                </p>
              </div>
            }
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {currentConnections.map((connection) => (
                <div
                  key={connection.id}
                  className="relative rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm truncate">{connection.name}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant="ghost-info"
                            size="sm"
                          >
                            {connection.type.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveConnection(connection.id)}
                      disabled={isLoading}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10 shrink-0"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </If>
        </div>

        <Separator />

        {/* Available Connections */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-foreground">Available Connections</h4>
            <Badge variant="outline" size="sm">
              {availableBuiltinConnections.length + availableMcpConnections.length}
            </Badge>
          </div>

          <If
            condition={(availableBuiltinConnections.length + availableMcpConnections.length) > 0}
            fallback={
              <div className="rounded-lg border border-dashed bg-muted/20 p-6 text-center">
                <Link className="mx-auto h-8 w-8 text-muted-foreground/50" />
                <p className="mt-2 text-sm text-muted-foreground">
                  All connections are assigned
                </p>
              </div>
            }
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {[...availableBuiltinConnections, ...availableMcpConnections].map((connection) => (
                <div
                  key={connection.id}
                  className="relative rounded-lg border border-dashed bg-card/50 p-4 transition-all hover:border-solid hover:shadow-md hover:bg-card"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-sm truncate">{connection.name}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant="outline"
                            size="sm"
                          >
                            {connection.type.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleAddConnection(connection.id)}
                      disabled={isLoading}
                      className="text-primary hover:text-primary hover:bg-primary/10 shrink-0"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </If>
        </div>
      </div>
    </div>
  );
}
